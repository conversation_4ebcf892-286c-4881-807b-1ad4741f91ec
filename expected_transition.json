{"transitions": [{"id": "transition-WorkflowExecutor-1234567890", "sequence": 1, "transition_type": "standard", "execution_type": "workflow", "node_label": "Execute Workflow", "node_info": {"node_id": "WorkflowExecutor", "tools_to_use": [{"tool_id": 1, "tool_name": "WorkflowExecutor", "tool_params": {"items": [{"field_name": "workflow_id", "data_type": "string", "field_value": "test-workflow"}, {"field_name": "field_mappings", "data_type": "object", "field_value": {"input1": "transition-2", "input2": "transition-2"}}]}}], "input_data": [{"from_transition_id": "transition-previous-node", "handle_mappings": [{"source_handle_id": "topic_output", "target_handle_id": "input1", "edge_id": "reactflow__edge-PreviousNode-topic_output-WorkflowExecutor-input1"}, {"source_handle_id": "voice_output", "target_handle_id": "input2", "edge_id": "reactflow__edge-PreviousNode-voice_output-WorkflowExecutor-input2"}]}], "output_data": [{"to_transition_id": "transition-next-node", "target_node_id": "NextNode", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "execution_status", "result_path": "execution_status", "edge_id": "reactflow__edge-WorkflowExecutor-execution_status-NextNode-input"}]}}]}, "result_resolution": {"node_type": "workflow", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "input1", "handle_name": "Topic Input", "data_type": "string", "required": true, "description": "First input field for the target workflow"}, {"handle_id": "input2", "handle_name": "Voice Input", "data_type": "string", "required": true, "description": "Second input field for the target workflow"}], "output_handles": [{"handle_id": "execution_status", "handle_name": "Execution Status", "data_type": "string", "description": "Status of the workflow execution"}, {"handle_id": "workflow_execution_id", "handle_name": "Execution ID", "data_type": "string", "description": "ID of the triggered workflow"}, {"handle_id": "message", "handle_name": "Message", "data_type": "string", "description": "Status or error message"}]}, "result_path_hints": {"execution_status": "execution_status", "workflow_execution_id": "workflow_execution_id", "message": "message"}}, "approval_required": false, "end": false}]}